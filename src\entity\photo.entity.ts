import {
  Table,
  Column,
  DataType,
  BelongsTo,
  ForeignKey,
  Model,
} from 'sequelize-typescript';
import { Mountain } from './mountain.entity';
import { WaterSystem } from './water-system.entity';
import { HistoricalElement } from './historical-element.entity';

export interface PhotoAttributes {
  /** ID */
  id: number;
  /** 名称 */
  name: string;
  /** 照片链接 */
  url: string;
  /** 山塬ID */
  mountainId?: number;
  /** 水系ID */
  waterSystemId?: number;
  /** 历史要素ID */
  historicalElementId?: number;
}

/**
 * 照片表模型
 */
@Table({
  tableName: 'photo',
  comment: '照片表',
})
export class Photo extends Model<PhotoAttributes> implements PhotoAttributes {
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  })
  id: number;

  @Column({
    type: DataType.STRING(255),
    allowNull: false,
    comment: '名称',
  })
  name: string;

  @Column({
    type: DataType.STRING(255),
    allowNull: false,
    comment: '照片链接',
  })
  url: string;

  @ForeignKey(() => Mountain)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '山塬ID',
    field: 'mountain_id',
  })
  mountainId: number;

  @ForeignKey(() => WaterSystem)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '水系ID',
    field: 'water_system_id',
  })
  waterSystemId: number;

  @ForeignKey(() => HistoricalElement)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '历史要素ID',
    field: 'historical_element_id',
  })
  historicalElementId: number;

  // 关联关系
  @BelongsTo(() => Mountain, 'mountainId')
  mountain: Mountain;

  @BelongsTo(() => WaterSystem, 'waterSystemId')
  waterSystem: WaterSystem;

  @BelongsTo(() => HistoricalElement, 'historicalElementId')
  historicalElement: HistoricalElement;
}
