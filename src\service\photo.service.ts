import { Provide, Inject } from '@midwayjs/core';
import { ModelCtor } from 'sequelize-typescript';
import { Op } from 'sequelize';
import { Photo } from '../entity/photo.entity';
import { PageQueryDTO, PageResponseDTO } from '../dto/common.dto';
import { CreatePhotoDTO, UpdatePhotoDTO } from '../dto/entity.dto';
import { CacheService } from './cache.service';
import { BaseService } from '../common/BaseService';

@Provide()
export class PhotoService extends BaseService<Photo> {
  @Inject()
  cacheService: CacheService;

  constructor() {
    super('照片');
  }

  protected getModel(): ModelCtor<Photo> {
    return Photo;
  }

  /**
   * 创建照片记录（业务逻辑封装）
   */
  async createPhoto(createDto: CreatePhotoDTO): Promise<Photo> {
    await this.validatePhotoData(createDto);
    return await this.create(createDto as any);
  }

  /**
   * 更新照片记录（业务逻辑封装）
   */
  async updatePhoto(id: number, updateDto: UpdatePhotoDTO): Promise<Photo> {
    await this.validatePhotoData(updateDto);
    await this.update({ id }, updateDto as any);
    return (await this.findById(id)) as Photo;
  }

  /**
   * 删除照片记录（业务逻辑封装）
   */
  async deletePhoto(id: number): Promise<void> {
    // TODO: 可以考虑是否同时删除物理文件
    await this.delete({ id });
  }

  /**
   * 分页查询照片列表
   */
  async findPhotoList(query: PageQueryDTO): Promise<PageResponseDTO<Photo>> {
    const { page, pageSize, keyword } = query;
    const offset = (page - 1) * pageSize;

    const whereConditions: any = {};

    if (keyword) {
      whereConditions.name = { [Symbol.for('like')]: `%${keyword}%` };
    }

    const result = await this.findAll({
      query: whereConditions,
      offset,
      limit: pageSize,
      order: [['createdAt', 'DESC']],
      include: [
        { association: 'mountain', required: false },
        { association: 'waterSystem', required: false },
        { association: 'historicalElement', required: false },
      ],
    });

    return new PageResponseDTO(result.list, result.total || 0, page, pageSize);
  }

  /**
   * 根据关联实体查询照片
   */
  async findPhotosByEntity(
    entityType: 'mountain' | 'waterSystem' | 'historicalElement',
    entityId: number
  ): Promise<Photo[]> {
    const whereConditions: any = {};
    
    switch (entityType) {
      case 'mountain':
        whereConditions.mountainId = entityId;
        break;
      case 'waterSystem':
        whereConditions.waterSystemId = entityId;
        break;
      case 'historicalElement':
        whereConditions.historicalElementId = entityId;
        break;
    }

    const result = await this.findAll({
      query: whereConditions,
      order: [['createdAt', 'DESC']],
    });

    return result.list;
  }

  /**
   * 批量创建照片记录
   */
  async batchCreatePhotos(photos: CreatePhotoDTO[]): Promise<Photo[]> {
    const createdPhotos: Photo[] = [];
    
    for (const photoDto of photos) {
      await this.validatePhotoData(photoDto);
      const photo = await this.create(photoDto as any);
      createdPhotos.push(photo);
    }
    
    return createdPhotos;
  }

  /**
   * 批量删除照片记录
   */
  async batchDeletePhotos(ids: number[]): Promise<void> {
    for (const id of ids) {
      await this.delete({ id });
    }
  }

  /**
   * 验证照片数据
   */
  private async validatePhotoData(data: CreatePhotoDTO | UpdatePhotoDTO): Promise<void> {
    // 验证关联实体的唯一性（一张照片只能关联一个实体）
    const entityCount = [data.mountainId, data.waterSystemId, data.historicalElementId]
      .filter(id => id !== undefined && id !== null).length;
    
    if (entityCount > 1) {
      throw new Error('一张照片只能关联一个实体（山塬、水系或历史要素）');
    }

    // 验证URL格式
    if (data.url && !this.isValidUrl(data.url)) {
      throw new Error('照片URL格式不正确');
    }
  }

  /**
   * 验证URL格式
   */
  private isValidUrl(url: string): boolean {
    try {
      // 支持相对路径和绝对路径
      return url.startsWith('/') || url.startsWith('http://') || url.startsWith('https://');
    } catch {
      return false;
    }
  }

  /**
   * 获取照片统计信息
   */
  async getPhotoStatistics(): Promise<{
    total: number;
    mountainPhotos: number;
    waterSystemPhotos: number;
    historicalElementPhotos: number;
    unassignedPhotos: number;
  }> {
    const PhotoModel = this.getModel();

    const total = await PhotoModel.count();
    const mountainPhotos = await PhotoModel.count({
      where: { mountainId: { [Op.ne]: null } }
    });
    const waterSystemPhotos = await PhotoModel.count({
      where: { waterSystemId: { [Op.ne]: null } }
    });
    const historicalElementPhotos = await PhotoModel.count({
      where: { historicalElementId: { [Op.ne]: null } }
    });
    const unassignedPhotos = await PhotoModel.count({
      where: {
        mountainId: null,
        waterSystemId: null,
        historicalElementId: null,
      },
    });

    return {
      total,
      mountainPhotos,
      waterSystemPhotos,
      historicalElementPhotos,
      unassignedPhotos,
    };
  }
}
