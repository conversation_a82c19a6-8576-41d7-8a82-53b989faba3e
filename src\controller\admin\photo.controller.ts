import {
  Controller,
  Post,
  Put,
  Del,
  Get,
  Body,
  Param,
  Query,
  Inject,
  Files,
} from '@midwayjs/core';
import { Validate } from '@midwayjs/validate';
import { UploadFileInfo } from '@midwayjs/upload';
import { JwtMiddleware } from '../../middleware/jwt.middleware';
import { AuthMiddleware } from '../../middleware/auth.middleware';
import { PhotoService } from '../../service/photo.service';
import { UploadService } from '../../service/upload.service';
import { PageQueryDTO } from '../../dto/common.dto';
import { CreatePhotoDTO, UpdatePhotoDTO } from '../../dto/entity.dto';
import { promises as fs } from 'fs';
import { join } from 'path';

/**
 * 照片管理控制器
 */
@Controller('/admin/photo', { middleware: [JwtMiddleware, AuthMiddleware] })
export class AdminPhotoController {
  @Inject()
  photoService: PhotoService;

  @Inject()
  uploadService: UploadService;

  /**
   * 创建照片记录
   */
  @Post('/')
  @Validate()
  async create(@Body() createDto: CreatePhotoDTO) {
    const data = await this.photoService.createPhoto(createDto);
    return data;
  }

  /**
   * 更新照片记录
   */
  @Put('/:id')
  @Validate()
  async update(@Param('id') id: number, @Body() updateDto: UpdatePhotoDTO) {
    const data = await this.photoService.updatePhoto(id, updateDto);
    return data;
  }

  /**
   * 删除照片记录
   */
  @Del('/:id')
  async delete(@Param('id') id: number) {
    await this.photoService.deletePhoto(id);
    return { message: '删除成功' };
  }

  /**
   * 获取照片列表
   */
  @Get('/')
  @Validate()
  async getList(@Query() query: PageQueryDTO) {
    const data = await this.photoService.findPhotoList(query);
    return data;
  }

  /**
   * 获取照片详情
   */
  @Get('/:id')
  async getDetail(@Param('id') id: number) {
    const data = await this.photoService.findById(id);
    if (!data) {
      throw new Error('照片记录不存在');
    }
    return data;
  }

  /**
   * 根据实体类型和ID获取照片列表
   */
  @Get('/entity/:entityType/:entityId')
  async getPhotosByEntity(
    @Param('entityType')
    entityType: 'mountain' | 'waterSystem' | 'historicalElement',
    @Param('entityId') entityId: number
  ) {
    const data = await this.photoService.findPhotosByEntity(
      entityType,
      entityId
    );
    return data;
  }

  /**
   * 批量创建照片记录
   */
  @Post('/batch')
  @Validate()
  async batchCreate(@Body() data: { photos: CreatePhotoDTO[] }) {
    const result = await this.photoService.batchCreatePhotos(data.photos);
    return { message: '批量创建成功', count: result.length, data: result };
  }

  /**
   * 批量删除照片记录
   */
  @Del('/batch')
  async batchDelete(@Body() data: { ids: number[] }) {
    await this.photoService.batchDeletePhotos(data.ids);
    return { message: '批量删除成功', count: data.ids.length };
  }

  /**
   * 获取照片统计信息
   */
  @Get('/statistics/overview')
  async getStatistics() {
    const data = await this.photoService.getPhotoStatistics();
    return data;
  }

  /**
   * 为实体批量关联照片
   */
  @Post('/associate/:entityType/:entityId')
  @Validate()
  async associatePhotos(
    @Param('entityType')
    entityType: 'mountain' | 'waterSystem' | 'historicalElement',
    @Param('entityId') entityId: number,
    @Body() data: { photoIds: number[] }
  ) {
    const updateData: Partial<UpdatePhotoDTO> = {};

    // 清除其他关联
    updateData.mountainId = null;
    updateData.waterSystemId = null;
    updateData.historicalElementId = null;

    // 设置新关联
    switch (entityType) {
      case 'mountain':
        updateData.mountainId = entityId;
        break;
      case 'waterSystem':
        updateData.waterSystemId = entityId;
        break;
      case 'historicalElement':
        updateData.historicalElementId = entityId;
        break;
      default:
        throw new Error('不支持的实体类型');
    }

    // 批量更新照片关联
    const updatedPhotos = [];
    for (const photoId of data.photoIds) {
      const photo = await this.photoService.updatePhoto(photoId, updateData);
      updatedPhotos.push(photo);
    }

    return {
      message: '关联成功',
      count: updatedPhotos.length,
      data: updatedPhotos,
    };
  }

  /**
   * 取消照片关联
   */
  @Post('/disassociate')
  @Validate()
  async disassociatePhotos(@Body() data: { photoIds: number[] }) {
    const updateData: Partial<UpdatePhotoDTO> = {
      mountainId: null,
      waterSystemId: null,
      historicalElementId: null,
    };

    const updatedPhotos = [];
    for (const photoId of data.photoIds) {
      const photo = await this.photoService.updatePhoto(photoId, updateData);
      updatedPhotos.push(photo);
    }

    return {
      message: '取消关联成功',
      count: updatedPhotos.length,
      data: updatedPhotos,
    };
  }

  /**
   * 上传照片文件
   */
  @Post('/upload')
  @Validate()
  async uploadPhoto(
    @Files() files: UploadFileInfo<any>[],
    @Query('photoName') photoName?: string,
    @Query('entityType')
    entityType?: 'mountain' | 'waterSystem' | 'historicalElement',
    @Query('entityId') entityId?: string
  ) {
    if (!files || files.length === 0) {
      throw new Error('请选择要上传的文件');
    }

    const file = files[0];

    // 验证文件类型
    if (!this.uploadService.validateFileType(file.filename)) {
      throw new Error('不支持的文件类型，仅支持图片文件');
    }

    // 验证文件大小
    const fileSize = this.getFileSize(file);
    if (!this.uploadService.validateFileSize(fileSize)) {
      throw new Error('文件大小超过限制（50MB）');
    }

    // 上传文件并创建照片记录
    const options = {
      createPhotoRecord: true,
      photoName: photoName || file.filename,
      entityType,
      entityId: entityId ? parseInt(entityId) : undefined,
    };

    const result = await this.uploadService.uploadFile(file, options);

    return {
      url: result.url,
      photoId: result.photoId,
      filename: file.filename,
      size: fileSize,
    };
  }

  /**
   * 删除照片文件
   */
  @Del('/:id/file')
  async deletePhotoFile(@Param('id') id: number) {
    // 获取照片信息
    const photo = await this.photoService.findById(id);
    if (!photo) {
      throw new Error('照片记录不存在');
    }

    // 删除物理文件
    if (photo.url) {
      try {
        await this.deletePhysicalFile(photo.url);
      } catch (error) {
        console.warn(`删除物理文件失败: ${photo.url}`, error);
        // 继续删除数据库记录，即使物理文件删除失败
      }
    }

    // 删除数据库记录
    await this.photoService.deletePhoto(id);

    return { message: '照片删除成功' };
  }

  /**
   * 获取文件大小
   */
  private getFileSize(file: UploadFileInfo<any>): number {
    if (typeof file.data === 'string') {
      return Buffer.byteLength(file.data);
    } else if (Buffer.isBuffer(file.data)) {
      return file.data.length;
    } else if (
      file.data &&
      typeof file.data === 'object' &&
      'length' in file.data
    ) {
      return (file.data as any).length || 0;
    } else {
      return (file as any).size || 0;
    }
  }

  /**
   * 删除物理文件
   */
  private async deletePhysicalFile(url: string): Promise<void> {
    // 将URL转换为文件路径
    const relativePath = url.replace(/^\//, '');
    const filePath = join(process.cwd(), relativePath);

    try {
      await fs.access(filePath);
      await fs.unlink(filePath);
      console.log(`✅ 删除文件成功: ${filePath}`);
    } catch (error) {
      throw new Error(`删除文件失败: ${filePath}`);
    }
  }
}
